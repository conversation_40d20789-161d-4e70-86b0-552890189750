# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyBDIyHH1tMwCl8KQn9ap-FAiJHgcphTxbA

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=127.0.0.1
FLASK_PORT=5000

# Chatbot Configuration
MAX_CONVERSATION_HISTORY=50
DEFAULT_MODEL=gemini-1.5-flash

# Enhanced ChatGPT-like Configuration
ENABLE_MARKDOWN_RENDERING=True
ENABLE_CODE_HIGHLIGHTING=True
ENABLE_MATH_RENDERING=True
CONVERSATION_TEMPERATURE=0.7
MAX_OUTPUT_TOKENS=2048
