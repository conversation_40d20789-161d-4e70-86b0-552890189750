# AI Chatbot - Powered by Google Gemini

A comprehensive AI chatbot application that integrates with Google's Gemini API, providing both web and command-line interfaces for interactive conversations.

## 🌟 Features

### Core Features
- **Dual Interface**: Both web-based and command-line interfaces
- **Google Gemini Integration**: Powered by Google's advanced Gemini AI model
- **Conversation History**: Persistent conversation tracking and management
- **Real-time Chat**: Instant responses with typing indicators
- **Export Functionality**: Export conversations in JSON or text format
- **Error Handling**: Robust error handling and connection management
- **Responsive Design**: Modern, mobile-friendly web interface
- **Secure Configuration**: Environment-based API key management

### 🚀 ChatGPT-like Enhancements
- **Enhanced Response Quality**: Configured with ChatGPT-style system prompts for helpful, detailed responses
- **Advanced Conversation Flow**: Improved context awareness with up to 20 messages of conversation history
- **Rich Text Formatting**: Full markdown support including:
  - **Bold** and *italic* text formatting
  - `Code snippets` with syntax highlighting
  - Code blocks with language-specific highlighting
  - Numbered and bulleted lists
  - Headers and blockquotes
  - Mathematical expressions (MathJax support)
- **Interactive Message Features**:
  - Copy message functionality
  - Regenerate response option for assistant messages
  - Message timestamps and metadata
- **Improved UI/UX**:
  - ChatGPT-style message bubbles and spacing
  - Enhanced typography and readability
  - Hover actions for better interaction
  - Professional gradient design
- **Smart System Prompts**: Configured to behave like ChatGPT with helpful, accurate, and conversational responses

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Google Gemini API key ([Get one here](https://makersuite.google.com/app/apikey))

### Installation

1. **Clone or download the project**
   ```bash
   cd "AI chatbot"
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   # Copy the example environment file
   cp .env.example .env
   
   # Edit .env and add your Gemini API key
   # GEMINI_API_KEY=your_actual_api_key_here
   ```

4. **Run the application**

   **Web Interface:**
   ```bash
   python app.py
   ```
   Then open http://127.0.0.1:5000 in your browser

   **Command-line Interface:**
   ```bash
   python cli_chatbot.py
   ```

## 📁 Project Structure

```
AI chatbot/
├── app.py                 # Flask web application
├── cli_chatbot.py         # Command-line interface
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── .gitignore           # Git ignore patterns
├── README.md            # This file
├── chatbot/             # Core chatbot package
│   ├── __init__.py      # Package initialization
│   ├── config.py        # Configuration management
│   ├── gemini_client.py # Gemini API integration
│   └── conversation.py  # Conversation management
├── static/              # Web interface assets
│   ├── css/
│   │   └── style.css    # Modern UI styling
│   └── js/
│       └── chat.js      # Frontend JavaScript
└── templates/           # HTML templates
    └── index.html       # Main chat interface
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Required: Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=127.0.0.1
FLASK_PORT=5000

# Optional: Chatbot Configuration
MAX_CONVERSATION_HISTORY=50
DEFAULT_MODEL=gemini-1.5-flash

# Enhanced ChatGPT-like Configuration
ENABLE_MARKDOWN_RENDERING=True
ENABLE_CODE_HIGHLIGHTING=True
ENABLE_MATH_RENDERING=True
CONVERSATION_TEMPERATURE=0.7
MAX_OUTPUT_TOKENS=2048
```

### Getting a Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key and add it to your `.env` file

## 💻 Usage

### Web Interface

1. Start the web server:
   ```bash
   python app.py
   ```

2. Open your browser and navigate to `http://127.0.0.1:5000`

3. Start chatting with the AI!

**Web Interface Features:**
- Real-time messaging with typing indicators
- Full markdown rendering with syntax highlighting
- Copy message functionality with visual feedback
- Regenerate response option for assistant messages
- Message timestamps and model information
- Export conversations (JSON/Text)
- Clear conversation functionality
- Responsive design for mobile devices
- Mathematical expression rendering (MathJax)
- Enhanced ChatGPT-like message styling

### Command-line Interface

1. Start the CLI chatbot:
   ```bash
   python cli_chatbot.py
   ```

2. Type your messages and press Enter

**CLI Commands:**
- `/help` - Show help message
- `/clear` - Clear conversation history
- `/history` - Show conversation history
- `/export` - Export conversation to file
- `/status` - Show connection status
- `/quit` - Exit the chatbot

## 🛠️ API Endpoints

The web application provides several API endpoints:

- `POST /api/chat` - Send a message to the chatbot (supports regeneration)
- `POST /api/regenerate` - Regenerate the last assistant response
- `GET /api/history` - Get conversation history with metadata
- `POST /api/clear` - Clear conversation history
- `GET /api/export` - Export conversation (supports `?format=json` or `?format=text`)
- `GET /api/status` - Get application and API status

## 🎨 Customization

### Styling

The web interface uses modern CSS with:
- Gradient backgrounds and modern design
- Responsive layout for all screen sizes
- Smooth animations and transitions
- Dark/light theme support (can be extended)

### Configuration

Modify `chatbot/config.py` to adjust:
- Maximum conversation history length
- Default AI model
- Flask server settings
- API timeout settings

## 🔒 Security

- API keys are stored in environment variables
- No sensitive data is logged
- CORS protection for web interface
- Input validation and sanitization

## 🐛 Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY is required" error**
   - Ensure your `.env` file exists and contains a valid API key
   - Check that the API key is correctly formatted

2. **Connection errors**
   - Verify your internet connection
   - Check if the Gemini API is accessible from your network
   - Ensure your API key has proper permissions

3. **Import errors**
   - Make sure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version compatibility (3.8+)

4. **Port already in use**
   - Change the port in `.env` file: `FLASK_PORT=5001`
   - Or kill the process using the port

### Debug Mode

Enable debug mode by setting in your `.env` file:
```env
FLASK_DEBUG=True
```

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions:
1. Check the troubleshooting section above
2. Review the error messages for specific guidance
3. Ensure all dependencies are properly installed
4. Verify your API key is valid and has proper permissions

---

**Happy Chatting! 🤖✨**
