"""
Flask web application for the AI Chatbot.
Provides a web interface for interacting with the Gemini-powered chatbot.
"""

from flask import <PERSON><PERSON>k, render_template, request, jsonify, session
from flask_cors import CORS
import uuid
from chatbot import Config, GeminiClient, ConversationManager

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'
CORS(app)

# Global storage for conversation managers (in production, use a database)
conversation_sessions = {}

def get_or_create_conversation_manager(session_id):
    """Get or create a conversation manager for the session."""
    if session_id not in conversation_sessions:
        conversation_sessions[session_id] = ConversationManager()
    return conversation_sessions[session_id]

@app.route('/')
def index():
    """Render the main chat interface."""
    # Create a new session ID if one doesn't exist
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages from the frontend with enhanced ChatGPT-like features."""
    try:
        # Validate configuration
        Config.validate_config()

        # Get session ID
        session_id = session.get('session_id')
        if not session_id:
            session['session_id'] = str(uuid.uuid4())
            session_id = session['session_id']

        # Get conversation manager
        conversation_manager = get_or_create_conversation_manager(session_id)

        # Get request data
        data = request.get_json()
        user_message = data.get('message', '').strip()
        regenerate = data.get('regenerate', False)

        if not user_message and not regenerate:
            return jsonify({'error': 'Message cannot be empty'}), 400

        # Initialize Gemini client
        gemini_client = GeminiClient()

        # Get conversation history for context
        history = conversation_manager.get_conversation_history()

        if regenerate:
            # For regeneration, get the last user message
            if not history:
                return jsonify({'error': 'No previous message to regenerate'}), 400

            # Find the last user message
            last_user_message = None
            for msg in reversed(history):
                if msg.get('type') == 'user':
                    last_user_message = msg.get('message')
                    break

            if not last_user_message:
                return jsonify({'error': 'No user message found to regenerate'}), 400

            # Remove the last assistant response if it exists
            if history and history[-1].get('type') == 'assistant':
                conversation_manager.conversation_history.pop()
                history = conversation_manager.get_conversation_history()

            # Generate new response for the last user message
            response_data = gemini_client.regenerate_response(last_user_message, history)
            user_message = last_user_message  # For response formatting
        else:
            # Add user message to conversation history
            conversation_manager.add_message(user_message, 'user')
            history = conversation_manager.get_conversation_history()

            # Generate response
            response_data = gemini_client.generate_response(user_message, history)

        if response_data['success']:
            # Add assistant response to conversation history
            message_metadata = {
                'model': response_data['model'],
                'regenerated': response_data.get('regenerated', False)
            }

            conversation_manager.add_message(
                response_data['response'],
                'assistant',
                message_metadata
            )

            return jsonify({
                'success': True,
                'response': response_data['response'],
                'conversation_id': session_id,
                'message_count': len(conversation_manager.get_conversation_history()),
                'model': response_data['model'],
                'regenerated': response_data.get('regenerated', False)
            })
        else:
            return jsonify({
                'success': False,
                'error': response_data['error']
            }), 500

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': f'An unexpected error occurred: {str(e)}'}), 500

@app.route('/api/regenerate', methods=['POST'])
def regenerate_response():
    """Regenerate the last assistant response."""
    try:
        # Validate configuration
        Config.validate_config()

        # Get session ID
        session_id = session.get('session_id')
        if not session_id:
            return jsonify({'error': 'No active session'}), 400

        # Get conversation manager
        conversation_manager = get_or_create_conversation_manager(session_id)
        history = conversation_manager.get_conversation_history()

        if not history:
            return jsonify({'error': 'No conversation history to regenerate'}), 400

        # Find the last user message
        last_user_message = None
        for msg in reversed(history):
            if msg.get('type') == 'user':
                last_user_message = msg.get('message')
                break

        if not last_user_message:
            return jsonify({'error': 'No user message found'}), 400

        # Remove the last assistant response if it exists
        if history and history[-1].get('type') == 'assistant':
            conversation_manager.conversation_history.pop()
            history = conversation_manager.get_conversation_history()

        # Initialize Gemini client and regenerate response
        gemini_client = GeminiClient()
        response_data = gemini_client.regenerate_response(last_user_message, history)

        if response_data['success']:
            # Add the new response to conversation history
            conversation_manager.add_message(
                response_data['response'],
                'assistant',
                {
                    'model': response_data['model'],
                    'regenerated': True
                }
            )

            return jsonify({
                'success': True,
                'response': response_data['response'],
                'conversation_id': session_id,
                'message_count': len(conversation_manager.get_conversation_history()),
                'regenerated': True
            })
        else:
            return jsonify({
                'success': False,
                'error': response_data['error']
            }), 500

    except Exception as e:
        return jsonify({'error': f'Failed to regenerate response: {str(e)}'}), 500

@app.route('/api/history')
def get_history():
    """Get conversation history for the current session."""
    session_id = session.get('session_id')
    if not session_id:
        return jsonify({'history': []})
    
    conversation_manager = get_or_create_conversation_manager(session_id)
    history = conversation_manager.get_conversation_history()
    
    return jsonify({'history': history})

@app.route('/api/clear', methods=['POST'])
def clear_conversation():
    """Clear the conversation history for the current session."""
    session_id = session.get('session_id')
    if session_id and session_id in conversation_sessions:
        conversation_sessions[session_id].clear_conversation()
    
    return jsonify({'success': True, 'message': 'Conversation cleared'})

@app.route('/api/export')
def export_conversation():
    """Export conversation history."""
    session_id = session.get('session_id')
    if not session_id or session_id not in conversation_sessions:
        return jsonify({'error': 'No conversation to export'}), 404
    
    conversation_manager = conversation_sessions[session_id]
    export_format = request.args.get('format', 'json')
    
    try:
        exported_data = conversation_manager.export_conversation(export_format)
        return jsonify({
            'success': True,
            'data': exported_data,
            'format': export_format
        })
    except ValueError as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/status')
def get_status():
    """Get application and API status."""
    try:
        Config.validate_config()
        gemini_client = GeminiClient()
        connection_test = gemini_client.test_connection()
        
        return jsonify({
            'app_status': 'running',
            'api_connection': connection_test,
            'model': Config.DEFAULT_MODEL
        })
    except Exception as e:
        return jsonify({
            'app_status': 'error',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    try:
        # Validate configuration before starting
        Config.validate_config()
        print("✅ Configuration validated successfully")
        print(f"🚀 Starting chatbot server on {Config.FLASK_HOST}:{Config.FLASK_PORT}")
        
        # Start the Flask application
        app.run(**Config.get_flask_config())
        
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("Please check your .env file and ensure GEMINI_API_KEY is set.")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
