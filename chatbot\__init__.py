"""
AI Chatbot Package

A comprehensive chatbot application that integrates with Google's Gemini API.
Provides both web and command-line interfaces for interactive conversations.
"""

__version__ = "1.0.0"
__author__ = "AI Chatbot Developer"

from .config import Config
from .gemini_client import GeminiClient
from .conversation import ConversationManager

__all__ = ['Config', 'GeminiClient', 'ConversationManager']
