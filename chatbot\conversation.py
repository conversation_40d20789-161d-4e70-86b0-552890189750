"""
Conversation management for the AI Chatbot application.
Handles conversation history, message storage, and session management.
"""

import json
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from .config import Config

class ConversationManager:
    """Manages conversation history and message storage."""
    
    def __init__(self, max_history: Optional[int] = None):
        """
        Initialize the conversation manager.
        
        Args:
            max_history: Maximum number of messages to keep in history
        """
        self.max_history = max_history or Config.MAX_CONVERSATION_HISTORY
        self.conversation_history: List[Dict[str, Any]] = []
        self.session_start_time = time.time()
    
    def add_message(self, message: str, message_type: str, metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Add a message to the conversation history.
        
        Args:
            message: The message content
            message_type: Type of message ('user' or 'assistant')
            metadata: Additional metadata for the message
            
        Returns:
            The message entry that was added
        """
        message_entry = {
            'id': len(self.conversation_history) + 1,
            'message': message,
            'type': message_type,
            'timestamp': time.time(),
            'formatted_time': datetime.now().strftime('%H:%M:%S'),
            'metadata': metadata or {}
        }
        
        self.conversation_history.append(message_entry)
        
        # Trim history if it exceeds max length
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]
        
        return message_entry
    
    def get_conversation_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get the conversation history.
        
        Args:
            limit: Maximum number of recent messages to return
            
        Returns:
            List of conversation messages
        """
        if limit:
            return self.conversation_history[-limit:]
        return self.conversation_history.copy()
    
    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()
        self.session_start_time = time.time()
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current conversation.
        
        Returns:
            Dictionary with conversation statistics
        """
        user_messages = [msg for msg in self.conversation_history if msg['type'] == 'user']
        assistant_messages = [msg for msg in self.conversation_history if msg['type'] == 'assistant']
        
        return {
            'total_messages': len(self.conversation_history),
            'user_messages': len(user_messages),
            'assistant_messages': len(assistant_messages),
            'session_duration': time.time() - self.session_start_time,
            'session_start': datetime.fromtimestamp(self.session_start_time).strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def export_conversation(self, format_type: str = 'json') -> str:
        """
        Export the conversation history.
        
        Args:
            format_type: Export format ('json' or 'text')
            
        Returns:
            Formatted conversation data
        """
        if format_type == 'json':
            return json.dumps({
                'conversation': self.conversation_history,
                'summary': self.get_conversation_summary()
            }, indent=2)
        
        elif format_type == 'text':
            lines = [f"Conversation Export - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", "=" * 50]
            
            for msg in self.conversation_history:
                timestamp = msg['formatted_time']
                message_type = msg['type'].title()
                content = msg['message']
                lines.append(f"[{timestamp}] {message_type}: {content}")
            
            return "\n".join(lines)
        
        else:
            raise ValueError(f"Unsupported export format: {format_type}")
    
    def search_messages(self, query: str, case_sensitive: bool = False) -> List[Dict[str, Any]]:
        """
        Search for messages containing a specific query.
        
        Args:
            query: Search query
            case_sensitive: Whether to perform case-sensitive search
            
        Returns:
            List of matching messages
        """
        if not case_sensitive:
            query = query.lower()
        
        matching_messages = []
        for msg in self.conversation_history:
            message_text = msg['message'] if case_sensitive else msg['message'].lower()
            if query in message_text:
                matching_messages.append(msg)
        
        return matching_messages
