"""
Google Gemini API client for the AI Chatbot application.
Handles API communication, error handling, and response processing.
"""

import google.generativeai as genai
import time
from typing import Optional, Dict, Any
from .config import Config

class GeminiClient:
    """Client for interacting with Google's Gemini API."""
    
    def __init__(self, api_key: Optional[str] = None, model_name: Optional[str] = None):
        """
        Initialize the Gemini client.
        
        Args:
            api_key: Google Gemini API key (defaults to config)
            model_name: Model name to use (defaults to config)
        """
        self.api_key = api_key or Config.GEMINI_API_KEY
        self.model_name = model_name or Config.DEFAULT_MODEL
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure the API
        genai.configure(api_key=self.api_key)
        
        # Initialize the model
        try:
            self.model = genai.GenerativeModel(self.model_name)
        except Exception as e:
            raise ConnectionError(f"Failed to initialize Gemini model: {str(e)}")
    
    def generate_response(self, message: str, conversation_history: Optional[list] = None) -> Dict[str, Any]:
        """
        Generate a response from the Gemini API.
        
        Args:
            message: User's input message
            conversation_history: Previous conversation context
            
        Returns:
            Dictionary containing response data and metadata
        """
        try:
            # Prepare the prompt with conversation context
            prompt = self._prepare_prompt(message, conversation_history)
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            # Process and return the response
            return {
                'success': True,
                'response': response.text,
                'model': self.model_name,
                'timestamp': time.time(),
                'error': None
            }
            
        except Exception as e:
            return {
                'success': False,
                'response': None,
                'model': self.model_name,
                'timestamp': time.time(),
                'error': str(e)
            }
    
    def _prepare_prompt(self, message: str, conversation_history: Optional[list] = None) -> str:
        """
        Prepare the prompt with conversation context.
        
        Args:
            message: Current user message
            conversation_history: Previous conversation messages
            
        Returns:
            Formatted prompt string
        """
        if not conversation_history:
            return message
        
        # Build context from recent conversation history
        context_lines = []
        for entry in conversation_history[-10:]:  # Last 10 exchanges
            if entry.get('type') == 'user':
                context_lines.append(f"User: {entry.get('message', '')}")
            elif entry.get('type') == 'assistant':
                context_lines.append(f"Assistant: {entry.get('message', '')}")
        
        if context_lines:
            context = "\n".join(context_lines)
            return f"Previous conversation:\n{context}\n\nUser: {message}"
        
        return message
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to the Gemini API.
        
        Returns:
            Dictionary with connection test results
        """
        try:
            test_response = self.generate_response("Hello, this is a connection test.")
            return {
                'success': test_response['success'],
                'message': 'Connection successful' if test_response['success'] else 'Connection failed',
                'error': test_response.get('error')
            }
        except Exception as e:
            return {
                'success': False,
                'message': 'Connection failed',
                'error': str(e)
            }
