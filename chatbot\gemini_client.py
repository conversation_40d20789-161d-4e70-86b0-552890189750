"""
Google Gemini API client for the AI Chatbot application.
Handles API communication, error handling, and response processing.
"""

import google.generativeai as genai
import time
from typing import Optional, Dict, Any
from .config import Config

class GeminiClient:
    """Client for interacting with Google's Gemini API with ChatGPT-like enhancements."""

    # ChatGPT-like system prompt for better conversational experience
    SYSTEM_PROMPT = """You are a helpful, knowledgeable, and conversational AI assistant. Your goal is to provide accurate, detailed, and engaging responses that are similar to ChatGPT's style. Please follow these guidelines:

1. **Be conversational and friendly**: Use a warm, approachable tone while maintaining professionalism.

2. **Provide comprehensive answers**: Give detailed explanations, examples, and context when helpful.

3. **Use proper formatting**: Structure your responses with:
   - **Bold text** for emphasis and headings
   - *Italic text* for subtle emphasis
   - `Code snippets` for technical terms
   - Numbered lists for step-by-step instructions
   - Bullet points for related items
   - Code blocks for longer code examples

4. **Maintain context**: Remember and reference previous parts of our conversation when relevant.

5. **Ask clarifying questions**: When a request is ambiguous, ask for clarification to provide better help.

6. **Offer follow-up suggestions**: Suggest related topics or next steps that might be helpful.

7. **Be accurate and honest**: If you're unsure about something, say so. Provide sources or suggest verification when appropriate.

8. **Adapt your communication style**: Match the complexity and formality level appropriate for the user's question.

Remember to be helpful, accurate, and engaging in all your responses."""

    def __init__(self, api_key: Optional[str] = None, model_name: Optional[str] = None):
        """
        Initialize the Gemini client with ChatGPT-like configuration.

        Args:
            api_key: Google Gemini API key (defaults to config)
            model_name: Model name to use (defaults to config)
        """
        self.api_key = api_key or Config.GEMINI_API_KEY
        self.model_name = model_name or Config.DEFAULT_MODEL

        if not self.api_key:
            raise ValueError("Gemini API key is required")

        # Configure the API
        genai.configure(api_key=self.api_key)

        # Initialize the model with enhanced generation config
        try:
            generation_config = genai.types.GenerationConfig(
                temperature=0.7,  # Balanced creativity and consistency
                top_p=0.8,        # Nucleus sampling for better quality
                top_k=40,         # Limit vocabulary for more focused responses
                max_output_tokens=2048,  # Allow longer responses
                stop_sequences=None,
            )

            # Try to initialize with system instruction, fallback if not supported
            try:
                self.model = genai.GenerativeModel(
                    model_name=self.model_name,
                    generation_config=generation_config,
                    system_instruction=self.SYSTEM_PROMPT
                )
            except TypeError:
                # Fallback for older API versions that don't support system_instruction
                self.model = genai.GenerativeModel(
                    model_name=self.model_name,
                    generation_config=generation_config
                )
        except Exception as e:
            raise ConnectionError(f"Failed to initialize Gemini model: {str(e)}")
    
    def generate_response(self, message: str, conversation_history: Optional[list] = None) -> Dict[str, Any]:
        """
        Generate a ChatGPT-like response from the Gemini API with enhanced context handling.

        Args:
            message: User's input message
            conversation_history: Previous conversation context

        Returns:
            Dictionary containing response data and metadata
        """
        try:
            # Try using the enhanced conversation format first
            try:
                conversation_parts = self._prepare_conversation_context(message, conversation_history)

                # Start a chat session for better context handling
                chat = self.model.start_chat(history=conversation_parts[:-1])
                response = chat.send_message(conversation_parts[-1]['parts'][0])

                response_text = response.text

            except Exception as chat_error:
                # Fallback to simple prompt method
                prompt = self._prepare_prompt(message, conversation_history)
                response = self.model.generate_content(prompt)
                response_text = response.text

            # Post-process response for better formatting
            formatted_response = self._post_process_response(response_text)

            # Process and return the response
            return {
                'success': True,
                'response': formatted_response,
                'model': self.model_name,
                'timestamp': time.time(),
                'error': None,
                'raw_response': response_text  # Keep original for debugging
            }

        except Exception as e:
            return {
                'success': False,
                'response': None,
                'model': self.model_name,
                'timestamp': time.time(),
                'error': str(e)
            }

    def _post_process_response(self, response_text: str) -> str:
        """
        Post-process the response for better formatting and ChatGPT-like presentation.

        Args:
            response_text: Raw response from the model

        Returns:
            Formatted response text
        """
        if not response_text:
            return response_text

        # Clean up common formatting issues
        formatted_text = response_text.strip()

        # Ensure proper spacing around markdown elements
        import re

        # Fix spacing around headers
        formatted_text = re.sub(r'\n(#{1,6})\s*([^\n]+)', r'\n\n\1 \2\n', formatted_text)

        # Fix spacing around code blocks
        formatted_text = re.sub(r'\n```([^\n]*)\n', r'\n\n```\1\n', formatted_text)
        formatted_text = re.sub(r'\n```\n', r'\n```\n\n', formatted_text)

        # Fix spacing around lists
        formatted_text = re.sub(r'\n(\d+\.|\*|\-)\s', r'\n\n\1 ', formatted_text)

        # Remove excessive newlines but preserve intentional spacing
        formatted_text = re.sub(r'\n{4,}', '\n\n\n', formatted_text)

        return formatted_text.strip()
    
    def _prepare_conversation_context(self, message: str, conversation_history: Optional[list] = None) -> list:
        """
        Prepare conversation context for the Gemini model using proper chat format.

        Args:
            message: Current user message
            conversation_history: Previous conversation messages

        Returns:
            List of conversation parts for the model
        """
        conversation_parts = []

        if conversation_history:
            # Add recent conversation history (last 20 messages for better context)
            recent_history = conversation_history[-20:] if len(conversation_history) > 20 else conversation_history

            for entry in recent_history:
                if entry.get('type') == 'user':
                    conversation_parts.append({
                        'role': 'user',
                        'parts': [entry.get('message', '')]
                    })
                elif entry.get('type') == 'assistant':
                    conversation_parts.append({
                        'role': 'model',
                        'parts': [entry.get('message', '')]
                    })

        # Add current user message
        conversation_parts.append({
            'role': 'user',
            'parts': [message]
        })

        return conversation_parts

    def _prepare_prompt(self, message: str, conversation_history: Optional[list] = None) -> str:
        """
        Prepare the prompt with enhanced conversation context and system instructions.

        Args:
            message: Current user message
            conversation_history: Previous conversation messages

        Returns:
            Formatted prompt string with system instructions
        """
        # Start with system prompt for better responses
        prompt_parts = [self.SYSTEM_PROMPT, ""]

        if conversation_history:
            # Build enhanced context from recent conversation history
            context_lines = []
            recent_history = conversation_history[-15:] if len(conversation_history) > 15 else conversation_history

            for entry in recent_history:
                if entry.get('type') == 'user':
                    context_lines.append(f"Human: {entry.get('message', '')}")
                elif entry.get('type') == 'assistant':
                    context_lines.append(f"Assistant: {entry.get('message', '')}")

            if context_lines:
                prompt_parts.append("Conversation history:")
                prompt_parts.extend(context_lines)
                prompt_parts.append("")

        prompt_parts.append(f"Human: {message}")
        prompt_parts.append("Assistant:")

        return "\n".join(prompt_parts)

    def regenerate_response(self, message: str, conversation_history: Optional[list] = None) -> Dict[str, Any]:
        """
        Regenerate a response with slightly different parameters for variety.

        Args:
            message: User's input message
            conversation_history: Previous conversation context

        Returns:
            Dictionary containing response data and metadata
        """
        try:
            # Use slightly higher temperature for more variety in regeneration
            temp_config = genai.types.GenerationConfig(
                temperature=0.9,  # Higher creativity for regeneration
                top_p=0.9,
                top_k=50,
                max_output_tokens=2048,
            )

            # Create a temporary model instance with different config
            try:
                temp_model = genai.GenerativeModel(
                    model_name=self.model_name,
                    generation_config=temp_config,
                    system_instruction=self.SYSTEM_PROMPT + "\n\nPlease provide a fresh perspective or alternative approach to this response."
                )
            except TypeError:
                # Fallback for older API versions
                temp_model = genai.GenerativeModel(
                    model_name=self.model_name,
                    generation_config=temp_config
                )

            # Generate response with the temporary model
            prompt = self._prepare_prompt(message, conversation_history)
            response = temp_model.generate_content(prompt)

            formatted_response = self._post_process_response(response.text)

            return {
                'success': True,
                'response': formatted_response,
                'model': self.model_name + " (regenerated)",
                'timestamp': time.time(),
                'error': None,
                'regenerated': True
            }

        except Exception as e:
            return {
                'success': False,
                'response': None,
                'model': self.model_name,
                'timestamp': time.time(),
                'error': str(e),
                'regenerated': True
            }

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to the Gemini API.
        
        Returns:
            Dictionary with connection test results
        """
        try:
            test_response = self.generate_response("Hello, this is a connection test.")
            return {
                'success': test_response['success'],
                'message': 'Connection successful' if test_response['success'] else 'Connection failed',
                'error': test_response.get('error')
            }
        except Exception as e:
            return {
                'success': False,
                'message': 'Connection failed',
                'error': str(e)
            }
