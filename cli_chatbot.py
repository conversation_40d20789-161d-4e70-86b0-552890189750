#!/usr/bin/env python3
"""
Command-line interface for the AI Chatbot.
Provides a terminal-based chat experience with the Gemini-powered chatbot.
"""

import sys
import os
from colorama import init, Fore, Back, Style
from chatbot import Config, GeminiClient, ConversationManager

# Initialize colorama for cross-platform colored output
init(autoreset=True)

class CLIChatBot:
    """Command-line interface for the AI chatbot."""
    
    def __init__(self):
        """Initialize the CLI chatbot."""
        self.conversation_manager = ConversationManager()
        self.gemini_client = None
        self.running = False
        
    def initialize(self):
        """Initialize the chatbot and validate configuration."""
        try:
            # Validate configuration
            Config.validate_config()
            
            # Initialize Gemini client
            self.gemini_client = GeminiClient()
            
            # Test connection
            print(f"{Fore.YELLOW}Testing connection to Gemini API...")
            connection_test = self.gemini_client.test_connection()
            
            if connection_test['success']:
                print(f"{Fore.GREEN}✅ Successfully connected to Gemini API")
                return True
            else:
                print(f"{Fore.RED}❌ Failed to connect to Gemini API: {connection_test['error']}")
                return False
                
        except ValueError as e:
            print(f"{Fore.RED}❌ Configuration error: {e}")
            print(f"{Fore.YELLOW}Please ensure your .env file is set up correctly with GEMINI_API_KEY")
            return False
        except Exception as e:
            print(f"{Fore.RED}❌ Initialization error: {e}")
            return False
    
    def print_header(self):
        """Print the chatbot header."""
        print(f"\n{Back.BLUE}{Fore.WHITE} AI CHATBOT - POWERED BY GOOGLE GEMINI {Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'=' * 60}")
        print(f"{Fore.GREEN}Welcome! Type your messages and press Enter to chat.")
        print(f"{Fore.YELLOW}Commands:")
        print(f"  {Fore.CYAN}/help{Fore.YELLOW}    - Show this help message")
        print(f"  {Fore.CYAN}/clear{Fore.YELLOW}   - Clear conversation history")
        print(f"  {Fore.CYAN}/history{Fore.YELLOW} - Show conversation history")
        print(f"  {Fore.CYAN}/export{Fore.YELLOW}  - Export conversation")
        print(f"  {Fore.CYAN}/status{Fore.YELLOW}  - Show connection status")
        print(f"  {Fore.CYAN}/quit{Fore.YELLOW}    - Exit the chatbot")
        print(f"{Fore.CYAN}{'=' * 60}\n")
    
    def print_message(self, message, sender="assistant", timestamp=None):
        """Print a formatted message."""
        if sender == "user":
            print(f"{Fore.BLUE}You: {Style.RESET_ALL}{message}")
        else:
            print(f"{Fore.GREEN}AI: {Style.RESET_ALL}{message}")
        
        if timestamp:
            print(f"{Fore.MAGENTA}    [{timestamp}]{Style.RESET_ALL}")
        print()
    
    def handle_command(self, command):
        """Handle special commands."""
        command = command.lower().strip()
        
        if command == "/help":
            self.print_header()
            return True
            
        elif command == "/clear":
            self.conversation_manager.clear_conversation()
            print(f"{Fore.GREEN}✅ Conversation history cleared.")
            return True
            
        elif command == "/history":
            self.show_history()
            return True
            
        elif command == "/export":
            self.export_conversation()
            return True
            
        elif command == "/status":
            self.show_status()
            return True
            
        elif command == "/quit":
            print(f"{Fore.YELLOW}Goodbye! Thanks for chatting.")
            return False
            
        else:
            print(f"{Fore.RED}Unknown command: {command}")
            print(f"{Fore.YELLOW}Type /help for available commands.")
            return True
    
    def show_history(self):
        """Display conversation history."""
        history = self.conversation_manager.get_conversation_history()
        
        if not history:
            print(f"{Fore.YELLOW}No conversation history available.")
            return
        
        print(f"{Fore.CYAN}Conversation History:")
        print(f"{Fore.CYAN}{'-' * 40}")
        
        for msg in history:
            timestamp = msg.get('formatted_time', '')
            sender = msg.get('type', 'unknown')
            content = msg.get('message', '')
            
            if sender == 'user':
                print(f"{Fore.BLUE}[{timestamp}] You: {Style.RESET_ALL}{content}")
            else:
                print(f"{Fore.GREEN}[{timestamp}] AI: {Style.RESET_ALL}{content}")
        
        print(f"{Fore.CYAN}{'-' * 40}")
        
        # Show summary
        summary = self.conversation_manager.get_conversation_summary()
        print(f"{Fore.MAGENTA}Total messages: {summary['total_messages']}")
        print(f"{Fore.MAGENTA}Session duration: {summary['session_duration']:.1f} seconds")
        print()
    
    def export_conversation(self):
        """Export conversation to file."""
        try:
            print(f"{Fore.YELLOW}Export format:")
            print(f"  {Fore.CYAN}1{Fore.YELLOW} - JSON")
            print(f"  {Fore.CYAN}2{Fore.YELLOW} - Text")
            
            choice = input(f"{Fore.YELLOW}Choose format (1-2): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                format_type = "json"
                extension = "json"
            elif choice == "2":
                format_type = "text"
                extension = "txt"
            else:
                print(f"{Fore.RED}Invalid choice.")
                return
            
            # Export conversation
            exported_data = self.conversation_manager.export_conversation(format_type)
            
            # Save to file
            filename = f"conversation_export.{extension}"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(exported_data)
            
            print(f"{Fore.GREEN}✅ Conversation exported to {filename}")
            
        except Exception as e:
            print(f"{Fore.RED}❌ Export failed: {e}")
    
    def show_status(self):
        """Show connection and system status."""
        print(f"{Fore.CYAN}System Status:")
        print(f"{Fore.CYAN}{'-' * 20}")
        
        # Test API connection
        if self.gemini_client:
            connection_test = self.gemini_client.test_connection()
            if connection_test['success']:
                print(f"{Fore.GREEN}✅ Gemini API: Connected")
            else:
                print(f"{Fore.RED}❌ Gemini API: {connection_test['error']}")
        
        # Show conversation stats
        summary = self.conversation_manager.get_conversation_summary()
        print(f"{Fore.YELLOW}📊 Messages: {summary['total_messages']}")
        print(f"{Fore.YELLOW}⏱️  Session: {summary['session_duration']:.1f}s")
        print(f"{Fore.YELLOW}🤖 Model: {Config.DEFAULT_MODEL}")
        print()
    
    def run(self):
        """Run the main chat loop."""
        if not self.initialize():
            return
        
        self.print_header()
        self.running = True
        
        try:
            while self.running:
                # Get user input
                try:
                    user_input = input(f"{Fore.BLUE}You: {Style.RESET_ALL}").strip()
                except (KeyboardInterrupt, EOFError):
                    print(f"\n{Fore.YELLOW}Goodbye!")
                    break
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    self.running = self.handle_command(user_input)
                    continue
                
                # Add user message to history
                self.conversation_manager.add_message(user_input, 'user')
                
                # Show thinking indicator
                print(f"{Fore.YELLOW}AI is thinking...", end='', flush=True)
                
                # Get AI response
                try:
                    history = self.conversation_manager.get_conversation_history()
                    response_data = self.gemini_client.generate_response(user_input, history)
                    
                    # Clear thinking indicator
                    print(f"\r{' ' * 20}\r", end='')
                    
                    if response_data['success']:
                        # Add assistant response to history
                        self.conversation_manager.add_message(
                            response_data['response'], 
                            'assistant'
                        )
                        
                        # Print the response
                        self.print_message(response_data['response'], "assistant")
                    else:
                        print(f"{Fore.RED}❌ Error: {response_data['error']}")
                        print()
                        
                except Exception as e:
                    print(f"\r{' ' * 20}\r", end='')
                    print(f"{Fore.RED}❌ Unexpected error: {e}")
                    print()
                    
        except Exception as e:
            print(f"{Fore.RED}❌ Fatal error: {e}")

def main():
    """Main entry point for the CLI chatbot."""
    chatbot = CLIChatBot()
    chatbot.run()

if __name__ == "__main__":
    main()
