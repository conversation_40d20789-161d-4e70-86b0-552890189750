/* Modern AI Chatbot Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.chat-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bot-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bot-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.bot-details h1 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.status {
    opacity: 0.8;
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Messages Area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8fafc;
}

.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
    align-items: flex-start;
    gap: 0.75rem;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 75%;
    position: relative;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.message-text {
    padding: 1rem 1.25rem;
    border-radius: 1.5rem;
    line-height: 1.6;
}

.message.user .message-text {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border-bottom-right-radius: 0.5rem;
}

.message.assistant .message-text {
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom-left-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    color: #1e293b;
}

/* Enhanced markdown support */
.message-text h1, .message-text h2, .message-text h3,
.message-text h4, .message-text h5, .message-text h6 {
    margin: 1rem 0 0.5rem 0;
    font-weight: 600;
    line-height: 1.3;
}

.message-text h1 { font-size: 1.5rem; }
.message-text h2 { font-size: 1.3rem; }
.message-text h3 { font-size: 1.1rem; }

.message-text p {
    margin: 0.5rem 0;
}

.message-text ul, .message-text ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-text li {
    margin: 0.25rem 0;
}

.message-text code {
    background: #f1f5f9;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}

.message.user .message-text code {
    background: rgba(255, 255, 255, 0.2);
}

.message-text pre {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    margin: 0.5rem 0;
}

.message-text pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

.message-text blockquote {
    border-left: 4px solid #e2e8f0;
    padding-left: 1rem;
    margin: 0.5rem 0;
    font-style: italic;
    color: #64748b;
}

.message-text strong {
    font-weight: 600;
}

.message-text em {
    font-style: italic;
}

.message-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
    margin-top: 0.25rem;
}

.message:hover .message-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: 1px solid #e2e8f0;
    color: #64748b;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.action-btn:hover {
    background: #f1f5f9;
    color: #1e293b;
    border-color: #cbd5e1;
}

.regenerate-btn {
    background: #fef3c7;
    border-color: #fbbf24;
    color: #92400e;
}

.regenerate-btn:hover {
    background: #fde68a;
    border-color: #f59e0b;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.6;
    margin-top: 0.5rem;
    color: #64748b;
}

.welcome-message {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.welcome-message .bot-avatar {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: #64748b;
}

.typing-animation {
    display: flex;
    gap: 0.25rem;
}

.typing-animation span {
    width: 8px;
    height: 8px;
    background: #64748b;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Input Area */
.chat-input-container {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 1rem 1.5rem;
}

.chat-input {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 1.5rem;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease;
}

.chat-input:focus-within {
    border-color: #4f46e5;
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
}

.send-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #64748b;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 1rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #1e293b;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #64748b;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.modal-body {
    padding: 1.5rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    margin: 0.25rem;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
}

.btn-secondary {
    background: #64748b;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.export-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .bot-info {
        flex-direction: column;
        text-align: center;
    }
}
