// AI Chatbot Frontend JavaScript with ChatGPT-like enhancements
class ChatBot {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.chatMessages = document.getElementById('chatMessages');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.charCount = document.getElementById('charCount');
        this.clearBtn = document.getElementById('clearBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.status = document.getElementById('status');

        this.isLoading = false;
        this.lastMessageId = 0;
        this.init();
        this.setupMarkdown();
    }

    init() {
        this.setupEventListeners();
        this.checkStatus();
        this.loadConversationHistory();
        this.messageInput.focus();
    }

    setupMarkdown() {
        // Configure marked for better markdown rendering
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                highlight: function(code, lang) {
                    if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {}
                    }
                    return code;
                },
                breaks: true,
                gfm: true
            });
        }

        // Configure MathJax if available
        if (typeof MathJax !== 'undefined') {
            MathJax.config = {
                tex: {
                    inlineMath: [['$', '$'], ['\\(', '\\)']],
                    displayMath: [['$$', '$$'], ['\\[', '\\]']]
                }
            };
        }
    }

    setupEventListeners() {
        // Send message events
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.autoResizeTextarea();
        });

        // Clear conversation
        this.clearBtn.addEventListener('click', () => this.clearConversation());

        // Export conversation
        this.exportBtn.addEventListener('click', () => this.showExportModal());

        // Modal events
        this.setupModalEvents();
    }

    setupModalEvents() {
        // Error modal
        const errorModal = document.getElementById('errorModal');
        const closeErrorModal = document.getElementById('closeErrorModal');
        closeErrorModal.addEventListener('click', () => {
            errorModal.style.display = 'none';
        });

        // Export modal
        const exportModal = document.getElementById('exportModal');
        const closeExportModal = document.getElementById('closeExportModal');
        closeExportModal.addEventListener('click', () => {
            exportModal.style.display = 'none';
        });

        // Close modals on outside click
        window.addEventListener('click', (e) => {
            if (e.target === errorModal) {
                errorModal.style.display = 'none';
            }
            if (e.target === exportModal) {
                exportModal.style.display = 'none';
            }
        });
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isLoading) return;

        // Add user message to chat
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.updateCharCount();
        this.autoResizeTextarea();

        // Show loading indicator
        this.setLoading(true);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });

            const data = await response.json();

            if (data.success) {
                const options = {
                    regenerated: data.regenerated || false,
                    model: data.model
                };
                this.addMessage(data.response, 'assistant', options);

                // Show model info if regenerated
                if (data.regenerated) {
                    this.showSuccess('Response regenerated successfully');
                }
            } else {
                this.showError(data.error || 'Failed to get response from AI');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.setLoading(false);
        }
    }

    addMessage(content, type, options = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.dataset.messageId = ++this.lastMessageId;

        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        // Format content with markdown support
        const formattedContent = this.formatMessage(content, type);

        // Create message actions
        const actionsHtml = this.createMessageActions(type, options);

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-text">${formattedContent}</div>
                <div class="message-actions">${actionsHtml}</div>
                <div class="message-time">${timeString}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);

        // Highlight code blocks if hljs is available
        if (typeof hljs !== 'undefined') {
            messageDiv.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        }

        // Render math if MathJax is available
        if (typeof MathJax !== 'undefined') {
            MathJax.typesetPromise([messageDiv]).catch((err) => console.log(err));
        }

        this.scrollToBottom();
        return messageDiv;
    }

    createMessageActions(type, options = {}) {
        let actions = [];

        // Copy button for all messages
        actions.push(`
            <button type="button" class="action-btn copy-btn" title="Copy message" onclick="chatBot.copyMessage(this)">
                <i class="fas fa-copy"></i>
            </button>
        `);

        // Regenerate button for assistant messages
        if (type === 'assistant') {
            actions.push(`
                <button type="button" class="action-btn regenerate-btn" title="Regenerate response" onclick="chatBot.regenerateResponse(this)">
                    <i class="fas fa-redo"></i> Regenerate
                </button>
            `);
        }

        return actions.join('');
    }

    formatMessage(content, type = 'assistant') {
        // Use marked for markdown rendering if available
        if (typeof marked !== 'undefined' && type === 'assistant') {
            try {
                return marked.parse(content);
            } catch (error) {
                console.warn('Markdown parsing failed, falling back to basic formatting:', error);
            }
        }

        // Fallback to basic formatting
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>');
    }

    copyMessage(button) {
        const messageDiv = button.closest('.message');
        const messageText = messageDiv.querySelector('.message-text');
        const textContent = messageText.textContent || messageText.innerText;

        navigator.clipboard.writeText(textContent).then(() => {
            // Show feedback
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.style.color = '#10b981';

            setTimeout(() => {
                button.innerHTML = originalIcon;
                button.style.color = '';
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            this.showError('Failed to copy message to clipboard');
        });
    }

    async regenerateResponse(button) {
        if (this.isLoading) return;

        try {
            this.setLoading(true);

            // Remove the current assistant message
            const messageDiv = button.closest('.message');
            messageDiv.remove();

            const response = await fetch('/api/regenerate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (data.success) {
                this.addMessage(data.response, 'assistant', { regenerated: true });
            } else {
                this.showError(data.error || 'Failed to regenerate response');
                // Re-add the original message if regeneration failed
                this.chatMessages.appendChild(messageDiv);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.setLoading(false);
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.sendBtn.disabled = loading;
        this.loadingIndicator.style.display = loading ? 'flex' : 'none';
        
        if (loading) {
            this.scrollToBottom();
        }
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    updateCharCount() {
        const count = this.messageInput.value.length;
        this.charCount.textContent = `${count}/2000`;
        
        if (count > 1800) {
            this.charCount.style.color = '#ef4444';
        } else if (count > 1500) {
            this.charCount.style.color = '#f59e0b';
        } else {
            this.charCount.style.color = '#64748b';
        }
    }

    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    async clearConversation() {
        if (!confirm('Are you sure you want to clear the conversation?')) {
            return;
        }

        try {
            const response = await fetch('/api/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                // Clear messages except welcome message
                const messages = this.chatMessages.querySelectorAll('.message');
                messages.forEach(msg => msg.remove());
                
                this.showSuccess('Conversation cleared successfully');
            } else {
                this.showError('Failed to clear conversation');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        }
    }

    async loadConversationHistory() {
        try {
            const response = await fetch('/api/history');
            const data = await response.json();

            if (data.history && data.history.length > 0) {
                // Clear existing messages except welcome
                const messages = this.chatMessages.querySelectorAll('.message');
                messages.forEach(msg => msg.remove());

                // Add historical messages with enhanced formatting
                data.history.forEach(msg => {
                    const options = {
                        regenerated: msg.metadata?.regenerated || false,
                        model: msg.metadata?.model
                    };
                    this.addMessage(msg.message, msg.type, options);
                });
            }
        } catch (error) {
            console.error('Failed to load conversation history:', error);
        }
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.app_status === 'running' && data.api_connection.success) {
                this.status.textContent = `Connected to ${data.model}`;
                this.status.style.color = '#10b981';
            } else {
                this.status.textContent = 'Connection issues detected';
                this.status.style.color = '#ef4444';
            }
        } catch (error) {
            this.status.textContent = 'Unable to connect to server';
            this.status.style.color = '#ef4444';
        }
    }

    showExportModal() {
        document.getElementById('exportModal').style.display = 'flex';
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorModal').style.display = 'flex';
    }

    showSuccess(message) {
        // Simple success notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            z-index: 1001;
            animation: fadeInUp 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Export conversation function
async function exportConversation(format) {
    try {
        const response = await fetch(`/api/export?format=${format}`);
        const data = await response.json();

        if (data.success) {
            const blob = new Blob([data.data], { 
                type: format === 'json' ? 'application/json' : 'text/plain' 
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `conversation.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            document.getElementById('exportModal').style.display = 'none';
        } else {
            alert('Failed to export conversation: ' + data.error);
        }
    } catch (error) {
        alert('Export failed: ' + error.message);
    }
}

// Global chatBot instance for external access
let chatBot;

// Initialize the chatbot when the page loads
document.addEventListener('DOMContentLoaded', () => {
    chatBot = new ChatBot();
});
