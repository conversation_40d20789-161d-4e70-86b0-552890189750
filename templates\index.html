<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chatbot - Powered by <PERSON></title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Markdown rendering support -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- Code syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <!-- Math rendering support -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <div class="header-content">
                <div class="bot-info">
                    <div class="bot-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="bot-details">
                        <h1>AI Chatbot</h1>
                        <p class="status" id="status">Powered by Google Gemini</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn-icon" id="clearBtn" title="Clear conversation">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn-icon" id="exportBtn" title="Export conversation">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="bot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">
                        <p>Hello! I'm your AI assistant powered by Google's Gemini. I'm here to help you with:</p>
                        <ul>
                            <li><strong>Answering questions</strong> on a wide range of topics</li>
                            <li><strong>Writing and editing</strong> content</li>
                            <li><strong>Problem solving</strong> and analysis</li>
                            <li><strong>Code assistance</strong> and programming help</li>
                            <li><strong>Creative tasks</strong> and brainstorming</li>
                        </ul>
                        <p>Feel free to ask me anything! I'll provide detailed, helpful responses with proper formatting.</p>
                    </div>
                    <div class="message-actions">
                        <button type="button" class="action-btn copy-btn" title="Copy message">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="loading-indicator" id="loadingIndicator" style="display: none;">
            <div class="typing-animation">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span>AI is thinking...</span>
        </div>

        <!-- Input Area -->
        <div class="chat-input-container">
            <div class="chat-input">
                <textarea 
                    id="messageInput" 
                    placeholder="Type your message here..." 
                    rows="1"
                    maxlength="2000"
                ></textarea>
                <button id="sendBtn" class="send-btn" title="Send message">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="char-count" id="charCount">0/2000</span>
                <span class="tip">Press Enter to send, Shift+Enter for new line</span>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                <button class="close-btn" id="closeErrorModal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal" id="exportModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-download"></i> Export Conversation</h3>
                <button class="close-btn" id="closeExportModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <button class="btn btn-primary" onclick="exportConversation('json')">
                        <i class="fas fa-code"></i> Export as JSON
                    </button>
                    <button class="btn btn-secondary" onclick="exportConversation('text')">
                        <i class="fas fa-file-text"></i> Export as Text
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
</body>
</html>
